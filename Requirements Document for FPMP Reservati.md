Requirements Document for FPMP Reservation System


1. Introduction
This document outlines the functional requirements for a reservation system for FPMP Sport park Mariepampoen. The system will allow users to make reservations for various fields including a soccer field, a multi-purpose field, a bolas field, and a patio.

2. User Roles
•	User: 
o	A user can make, view, update and cancel their own reservations
o	A user can view field availability
•	Employee: 
o	An employee can make, view, update, and cancel all reservations.
•	Administrator: 
o	The administrator can do everything the employee does. The administrator can also:
	Add, remove and edit employees/users
	Create fields and utilities
	Set hourly prices for fields & utilities
	Set night & day hourly prices for fields
	Set night pricing start time
	Set unavailability in system (maintenance, field unavailable)

3. System Features

3.1 Field Reservation
•	The system should allow users to reserve any of the available fields (soccer, multi-purpose, bolas, patio) for a specific date and time slot.
•	The system should prevent double booking of fields. If a field is already reserved for a specific time slot, it should not be available for reservation.
•	The system should allow users to view the availability of fields before making a reservation.
•	The fields can only be reserved during working hours (working hours to be determined)
•	A field reservation is only final when approved by the assigned employee.

3.2 Recurring Reservations
•	The system should support recurring reservations. Employees should be able to specify the frequency (daily, weekly, monthly) and duration (start date and end date) of the recurring reservation.

3.3 Utilities Reservation
•	The system should support the rental of field utilities. Every field has its own utilities (chairs, tables) that can be rented alongside the field reservations. Each utility has its own price per hour.

3.4 Hourly Rates
•	The system should calculate the cost of a reservation based on the hourly rate for each field, plus its utilities hourly rate. The hourly rate for each field may vary.

3.5 Reservation Management
•	The system should allow users to update or cancel their own existing reservations.
o	The system should allow setting a cut-off date for cancellation/updates.
•	The system should allow employees to update or cancel existing reservations.
•	The system should send a confirmation to the employee once a reservation is made, updated, or cancelled.

4. Platform Compatibility
•	The system should be accessible via web and mobile (iOS, Android) platforms.

5. Notifications
•	Each field (soccer etc.) can set an employee as contact person.
o	Notifications for this field will be sent to the specific employee
•	The system will send a user reservation creation notification to employee, with request to approve
o	On approval, a notification is sent to the user confirming reservation.
•	The system will send an update/cancellation confirmation to the employee on changes to the reservation.
•	The system will send an update/cancellation confirmation to the user on changes to their own reservation.
•	The notification can be done via email or an in-app notification, depending on the system’s design.

6. Non-Functional Requirements
•	The system should be user-friendly and intuitive to navigate.
•	The system should ensure data privacy and security in line with applicable regulations.
•	The system should be reliable and available 24/7, barring scheduled maintenance periods.


7. Quickbooks Integration

7.1 Invoice Generation
•	After a reservation is approved, the system should automatically generate an invoice in Quickbooks using the details of the reservation, such as the client’s name, the field reserved, the duration of the reservation, and the total cost based on the hourly rate.
•	The invoice should include all necessary details such as the date, invoice number, item descriptions, quantities, rates, and amounts.

7.2 Data Synchronization
•	The system should ensure that data between the reservation system and Quickbooks is synchronized in real time. Any changes made in one system should be automatically reflected in the other.
•	Any edits made to the reservation should be updated in Quickbooks as well to make sure the Invoice is synchronized.

8. Future Enhancements
•	Integration with payment systems for online payment of reservation fees.
•	The system should allow for switching between 4 languages, Papiamentu, English, Dutch and Spanish
•	The system should include a Reporting and Analytics feature to provide valuable insights about the usage and performance of the field reservation system.
