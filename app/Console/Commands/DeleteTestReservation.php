<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Reservation;

class DeleteTestReservation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reservation:delete-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete the test reservation with ID 3';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $reservation = Reservation::find(3);
        
        if ($reservation) {
            $reservation->delete();
            $this->info('Test reservation with ID 3 has been deleted.');
        } else {
            $this->error('Test reservation with ID 3 not found.');
        }
        
        return Command::SUCCESS;
    }
}
