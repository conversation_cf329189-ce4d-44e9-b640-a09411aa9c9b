<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class EnsureAdminUserExists extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:ensure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensure that at least one admin user exists in the system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Check if there's at least one admin user
        $adminExists = User::where('role', 'administrator')->exists();

        // If no admin user exists, create one
        if (!$adminExists) {
            User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('mitahanjabo'),
                'role' => 'administrator',
                'email_verified_at' => now(),
            ]);

            $this->info('Super Admin user created successfully!');
        } else {
            $this->info('Admin user already exists. No new admin user created.');
        }

        return Command::SUCCESS;
    }
}
