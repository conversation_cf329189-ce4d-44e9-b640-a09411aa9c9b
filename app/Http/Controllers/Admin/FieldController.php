<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Models\FieldRate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $fields = Field::with(['rate', 'contactEmployee'])->get();
        return view('admin.fields.index', compact('fields'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $employees = User::whereIn('role', ['employee', 'administrator'])->get();
        return view('admin.fields.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:soccer,multi-purpose,bolas,patio',
            'description' => 'nullable|string',
            'opening_time' => 'required|date_format:H:i',
            'closing_time' => 'required|date_format:H:i|after:opening_time',
            'contact_employee_id' => 'nullable|exists:users,id',
            'day_rate' => 'required|numeric|min:0',
            'night_rate' => 'nullable|numeric|min:0',
            'night_rate_start_time' => 'required_with:night_rate|date_format:H:i',
        ]);

        DB::beginTransaction();

        try {
            // Create the field
            $field = Field::create([
                'name' => $request->name,
                'type' => $request->type,
                'description' => $request->description,
                'opening_time' => $request->opening_time,
                'closing_time' => $request->closing_time,
                'contact_employee_id' => $request->contact_employee_id,
                'is_active' => true,
            ]);

            // Create the field rate
            FieldRate::create([
                'field_id' => $field->id,
                'day_rate' => $request->day_rate,
                'night_rate' => $request->night_rate,
                'night_rate_start_time' => $request->night_rate_start_time ?? '18:00:00',
            ]);

            DB::commit();

            return redirect()->route('admin.fields.index')
                ->with('success', 'Field created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors(['error' => 'An error occurred while creating the field: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $field = Field::with(['rate', 'contactEmployee', 'reservations' => function($query) {
            $query->whereIn('status', ['pending', 'approved'])
                  ->where('date', '>=', now()->format('Y-m-d'))
                  ->orderBy('date')
                  ->orderBy('start_time');
        }, 'reservations.user'])->findOrFail($id);

        return view('admin.fields.show', compact('field'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $field = Field::with('rate')->findOrFail($id);
        $employees = User::whereIn('role', ['employee', 'administrator'])->get();

        return view('admin.fields.edit', compact('field', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:soccer,multi-purpose,bolas,patio',
            'description' => 'nullable|string',
            'opening_time' => 'required|date_format:H:i',
            'closing_time' => 'required|date_format:H:i|after:opening_time',
            'contact_employee_id' => 'nullable|exists:users,id',
            'day_rate' => 'required|numeric|min:0',
            'night_rate' => 'nullable|numeric|min:0',
            'night_rate_start_time' => 'required_with:night_rate|date_format:H:i',
            'is_active' => 'boolean',
        ]);

        $field = Field::with('rate')->findOrFail($id);

        DB::beginTransaction();

        try {
            // Update the field
            $field->update([
                'name' => $request->name,
                'type' => $request->type,
                'description' => $request->description,
                'opening_time' => $request->opening_time,
                'closing_time' => $request->closing_time,
                'contact_employee_id' => $request->contact_employee_id,
                'is_active' => $request->has('is_active'),
            ]);

            // Update the field rate
            $field->rate->update([
                'day_rate' => $request->day_rate,
                'night_rate' => $request->night_rate,
                'night_rate_start_time' => $request->night_rate_start_time ?? '18:00:00',
            ]);

            DB::commit();

            return redirect()->route('admin.fields.index')
                ->with('success', 'Field updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors(['error' => 'An error occurred while updating the field: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $field = Field::findOrFail($id);

        // Check if there are any active reservations for this field
        $activeReservations = $field->reservations()
            ->whereIn('status', ['pending', 'approved'])
            ->where('date', '>=', now()->format('Y-m-d'))
            ->count();

        if ($activeReservations > 0) {
            return back()->withErrors(['error' => 'Cannot delete field with active reservations.']);
        }

        // Instead of deleting, just mark as inactive
        $field->update(['is_active' => false]);

        return redirect()->route('admin.fields.index')
            ->with('success', 'Field marked as inactive successfully.');
    }
}
