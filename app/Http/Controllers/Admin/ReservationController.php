<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReservationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $reservations = Reservation::with(['field', 'user', 'approvedBy'])
            ->latest()
            ->paginate(20);

        return view('admin.reservations.index', compact('reservations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $fields = Field::where('is_active', true)->get();
        $users = User::all();

        return view('admin.reservations.create', compact('fields', 'users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'field_id' => 'required|exists:fields,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,approved',
        ]);

        $field = Field::with('rate')->findOrFail($request->field_id);

        // Check if the field is available for the requested time
        if (!$field->isAvailable($request->date, $request->start_time, $request->end_time)) {
            return back()->withErrors(['time_conflict' => 'The field is not available for the selected time.'])->withInput();
        }

        // Calculate duration in hours
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // Calculate total price
        $rate = $field->rate;
        $hourlyRate = $rate->getRateForTime($request->start_time);
        $totalPrice = $hourlyRate * $durationHours;

        // Set cancellation deadline to 1 day before reservation
        $cancellationDeadline = Carbon::parse($request->date)->subDay();

        // Create the reservation
        $reservation = new Reservation([
            'user_id' => $request->user_id,
            'field_id' => $field->id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'status' => $request->status,
            'cancellation_deadline' => $cancellationDeadline,
            'notes' => $request->notes,
        ]);

        // If status is approved, set the approved_by and approved_at
        if ($request->status === 'approved') {
            $reservation->approved_by = Auth::id();
            $reservation->approved_at = now();
        }

        $reservation->save();

        return redirect()->route('admin.reservations.show', $reservation)
            ->with('success', 'Reservation created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $reservation = Reservation::with(['field', 'field.rate', 'user', 'approvedBy'])
            ->findOrFail($id);

        return view('admin.reservations.show', compact('reservation'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $reservation = Reservation::findOrFail($id);
        $fields = Field::where('is_active', true)->get();
        $users = User::all();

        return view('admin.reservations.edit', compact('reservation', 'fields', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $reservation = Reservation::findOrFail($id);

        $request->validate([
            'user_id' => 'required|exists:users,id',
            'field_id' => 'required|exists:fields,id',
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,approved,rejected,cancelled',
        ]);

        $field = Field::with('rate')->findOrFail($request->field_id);

        // Check if the field is available for the requested time (excluding this reservation)
        $conflictingReservations = $field->reservations()
            ->where('date', $request->date)
            ->where('id', '!=', $reservation->id)
            ->where(function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('start_time', '<', $request->end_time)
                      ->where('end_time', '>', $request->start_time);
                });
            })
            ->whereIn('status', ['pending', 'approved'])
            ->count();

        if ($conflictingReservations > 0) {
            return back()->withErrors(['time_conflict' => 'The field is not available for the selected time.'])->withInput();
        }

        // Calculate duration in hours
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // Calculate total price
        $rate = $field->rate;
        $hourlyRate = $rate->getRateForTime($request->start_time);
        $totalPrice = $hourlyRate * $durationHours;

        // Set cancellation deadline to 1 day before reservation
        $cancellationDeadline = Carbon::parse($request->date)->subDay();

        // Update the reservation
        $reservation->update([
            'user_id' => $request->user_id,
            'field_id' => $field->id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'status' => $request->status,
            'cancellation_deadline' => $cancellationDeadline,
            'notes' => $request->notes,
        ]);

        // If status is being changed to approved, set the approved_by and approved_at
        if ($request->status === 'approved' && $reservation->status !== 'approved') {
            $reservation->update([
                'approved_by' => Auth::id(),
                'approved_at' => now(),
            ]);
        }

        return redirect()->route('admin.reservations.show', $reservation)
            ->with('success', 'Reservation updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $reservation = Reservation::findOrFail($id);

        // Update status to cancelled instead of deleting
        $reservation->update([
            'status' => 'cancelled',
        ]);

        return redirect()->route('admin.reservations.index')
            ->with('success', 'Reservation cancelled successfully.');
    }

    /**
     * Approve a reservation
     */
    public function approve(string $id)
    {
        $reservation = Reservation::findOrFail($id);

        if ($reservation->status === 'approved') {
            return redirect()->route('admin.reservations.show', $reservation)
                ->with('info', 'Reservation is already approved.');
        }

        $reservation->update([
            'status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
        ]);

        return redirect()->route('admin.reservations.show', $reservation)
            ->with('success', 'Reservation approved successfully.');
    }

    /**
     * Reject a reservation
     */
    public function reject(string $id)
    {
        $reservation = Reservation::findOrFail($id);

        if ($reservation->status === 'rejected') {
            return redirect()->route('admin.reservations.show', $reservation)
                ->with('info', 'Reservation is already rejected.');
        }

        $reservation->update([
            'status' => 'rejected',
        ]);

        return redirect()->route('admin.reservations.show', $reservation)
            ->with('success', 'Reservation rejected successfully.');
    }
}
