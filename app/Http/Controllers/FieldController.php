<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Reservation;
use Illuminate\Http\Request;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $fields = Field::where('is_active', true)->get();
        return view('fields.index', compact('fields'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Not needed for regular users
        return redirect()->route('fields.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Not needed for regular users
        return redirect()->route('fields.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $field = Field::with('rate')->findOrFail($id);
        return view('fields.show', compact('field'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Not needed for regular users
        return redirect()->route('fields.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Not needed for regular users
        return redirect()->route('fields.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Not needed for regular users
        return redirect()->route('fields.index');
    }

    /**
     * Check availability for a field
     */
    public function checkAvailability(Request $request, string $id)
    {
        $request->validate([
            'date' => 'required|date|after_or_equal:today',
        ]);

        $field = Field::findOrFail($id);
        $date = $request->date;

        // Get all reservations for this field on the selected date
        $reservations = $field->reservations()
            ->where('date', $date)
            ->whereIn('status', ['pending', 'approved'])
            ->get();

        return view('fields.availability', compact('field', 'date', 'reservations'));
    }

    /**
     * Show calendar view for field availability
     */
    public function calendar(?string $id = null)
    {
        // Get all active fields for the dropdown
        $allFields = Field::where('is_active', true)->get();

        if ($id) {
            try {
                $field = Field::findOrFail($id);
                $fields = $allFields; // Use all fields for the dropdown
            } catch (\Exception $e) {
                // If field not found, redirect to the main calendar view
                return redirect()->route('fields.calendar');
            }
        } else {
            $fields = $allFields;
            $field = null;
        }

        return view('fields.calendar', compact('fields', 'field'));
    }

    /**
     * Get reservations for calendar (JSON)
     */
    public function getReservations(Request $request)
    {
        // Log the request parameters
        \Log::info('Calendar API Request', [
            'start' => $request->start,
            'end' => $request->end,
            'field_id' => $request->field_id
        ]);

        // Log the request
        \Log::info('Calendar API Request received');

        // Build the query
        $query = Reservation::with(['field', 'user'])
            ->whereIn('status', ['pending', 'approved']);

        // Filter by field_id if provided
        if ($request->has('field_id') && $request->field_id !== null && $request->field_id !== '') {
            \Log::info('Filtering by field_id: ' . $request->field_id);
            $query->where('field_id', $request->field_id);
        } else {
            \Log::info('No field_id filter applied');
        }

        // Get the filtered reservations
        $reservations = $query->get();

        \Log::info('Found ' . $reservations->count() . ' active reservations');

        $events = [];

        foreach ($reservations as $reservation) {
            // Format dates for calendar
            $dateStr = $reservation->date->format('Y-m-d');
            $startTime = $reservation->start_time;
            $endTime = $reservation->end_time;

            // Ensure times have seconds
            if (strlen($startTime) === 5) $startTime .= ':00';
            if (strlen($endTime) === 5) $endTime .= ':00';

            $event = [
                'id' => $reservation->id,
                'title' => ($reservation->user->name ?? 'Unknown') . ' - ' . ($reservation->field->name ?? 'Unknown Field'),
                'start' => $dateStr . 'T' . $startTime,
                'end' => $dateStr . 'T' . $endTime,
                'allDay' => false,
                'url' => route('reservations.show', $reservation->id),
                'backgroundColor' => $reservation->status === 'pending' ? '#FBBF24' : '#34D399',
                'borderColor' => $reservation->status === 'pending' ? '#F59E0B' : '#10B981',
                'textColor' => $reservation->status === 'pending' ? '#92400E' : '#064E3B',
                'extendedProps' => [
                    'status' => $reservation->status,
                    'field_name' => $reservation->field->name ?? 'Unknown Field',
                    'user_name' => $reservation->user->name ?? 'Unknown',
                    'total_price' => $reservation->total_price,
                ]
            ];

            $events[] = $event;
        }

        \Log::info('Returning ' . count($events) . ' events');

        return response()->json($events);
    }
}
