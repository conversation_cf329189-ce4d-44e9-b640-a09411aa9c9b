<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReservationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $reservations = Auth::user()->reservations()->with('field')->latest()->get();
        return view('reservations.index', compact('reservations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $fields = Field::where('is_active', true)->get();
        return view('reservations.create', compact('fields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'field_id' => 'required|exists:fields,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
        ]);

        $field = Field::with('rate')->findOrFail($request->field_id);

        // Check if the field is available for the requested time
        if (!$field->isAvailable($request->date, $request->start_time, $request->end_time)) {
            return back()->withErrors(['time_conflict' => 'The field is not available for the selected time.'])->withInput();
        }

        // Calculate duration in hours
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // Calculate total price
        $rate = $field->rate;
        $hourlyRate = $rate->getRateForTime($request->start_time);
        $totalPrice = $hourlyRate * $durationHours;

        // Set cancellation deadline to 1 day before reservation
        $cancellationDeadline = Carbon::parse($request->date)->subDay();

        // Create the reservation
        $reservation = new Reservation([
            'user_id' => Auth::id(),
            'field_id' => $field->id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'status' => 'pending',
            'cancellation_deadline' => $cancellationDeadline,
            'notes' => $request->notes,
        ]);

        $reservation->save();

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation created successfully. It will be confirmed once approved by an employee.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $reservation = Reservation::with(['field', 'field.rate', 'user', 'approvedBy'])
            ->where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        return view('reservations.show', compact('reservation'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $reservation = Reservation::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // Check if the reservation can be edited (not approved yet or within cancellation deadline)
        if ($reservation->status !== 'pending' && !$reservation->canBeCancelled()) {
            return redirect()->route('reservations.show', $reservation)
                ->with('error', 'This reservation cannot be edited anymore.');
        }

        $fields = Field::where('is_active', true)->get();

        return view('reservations.edit', compact('reservation', 'fields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $reservation = Reservation::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // Check if the reservation can be updated
        if ($reservation->status !== 'pending' && !$reservation->canBeCancelled()) {
            return redirect()->route('reservations.show', $reservation)
                ->with('error', 'This reservation cannot be updated anymore.');
        }

        $request->validate([
            'field_id' => 'required|exists:fields,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
        ]);

        $field = Field::with('rate')->findOrFail($request->field_id);

        // Check if the field is available for the requested time (excluding this reservation)
        $conflictingReservations = $field->reservations()
            ->where('date', $request->date)
            ->where('id', '!=', $reservation->id)
            ->where(function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('start_time', '<', $request->end_time)
                      ->where('end_time', '>', $request->start_time);
                });
            })
            ->whereIn('status', ['pending', 'approved'])
            ->count();

        if ($conflictingReservations > 0) {
            return back()->withErrors(['time_conflict' => 'The field is not available for the selected time.'])->withInput();
        }

        // Calculate duration in hours
        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);
        $durationHours = $startTime->diffInMinutes($endTime) / 60;

        // Calculate total price
        $rate = $field->rate;
        $hourlyRate = $rate->getRateForTime($request->start_time);
        $totalPrice = $hourlyRate * $durationHours;

        // Set cancellation deadline to 1 day before reservation
        $cancellationDeadline = Carbon::parse($request->date)->subDay();

        // Update the reservation
        $reservation->update([
            'field_id' => $field->id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'status' => 'pending', // Reset to pending if it was approved
            'approved_by' => null,
            'approved_at' => null,
            'cancellation_deadline' => $cancellationDeadline,
            'notes' => $request->notes,
        ]);

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation updated successfully. It will need to be approved again.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $reservation = Reservation::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // Check if the reservation can be cancelled
        if (!$reservation->canBeCancelled()) {
            return redirect()->route('reservations.show', $reservation)
                ->with('error', 'This reservation cannot be cancelled anymore.');
        }

        // Update status to cancelled instead of deleting
        $reservation->update([
            'status' => 'cancelled',
        ]);

        return redirect()->route('reservations.index')
            ->with('success', 'Reservation cancelled successfully.');
    }
}
