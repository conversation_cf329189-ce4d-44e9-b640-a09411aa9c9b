<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // If no roles are specified, just check if the user is authenticated
        if (empty($roles)) {
            return $next($request);
        }

        // Check if the user has any of the required roles
        foreach ($roles as $role) {
            if ($request->user()->role === $role) {
                return $next($request);
            }
        }

        // If the user is an administrator, they can access any role-protected route
        if ($request->user()->isAdmin()) {
            return $next($request);
        }

        // If the user doesn't have any of the required roles, redirect to dashboard with an error
        return redirect()->route('dashboard')->with('error', 'You do not have permission to access this page.');
    }
}
