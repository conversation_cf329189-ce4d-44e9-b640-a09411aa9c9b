<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * The available application languages.
     *
     * @var array
     */
    protected $languages = ['en', 'es', 'nl', 'pap', 'it']; // Added 'it' for Italian

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the request has a 'lang' parameter
        if ($request->has('lang')) {
            $locale = $request->get('lang');
            
            // Verify if the requested language is supported
            if (in_array($locale, $this->languages)) {
                Session::put('locale', $locale);
                App::setLocale($locale);
            }
        } elseif (Session::has('locale')) {
            // Use the language from the session if available
            App::setLocale(Session::get('locale'));
        }

        return $next($request);
    }
}
