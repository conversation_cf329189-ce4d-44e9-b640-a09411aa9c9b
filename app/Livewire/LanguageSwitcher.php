<?php

namespace App\Livewire;

use Illuminate\Support\Facades\App;
use Livewire\Component;

class LanguageSwitcher extends Component
{
    /**
     * The available application languages.
     *
     * @var array
     */
    public $languages = [
        'en' => 'English',
        'es' => 'Spanish',
        'nl' => 'Dutch',
        'pap' => 'Papiamentu',
        'it' => 'Italian', // Added Italian
    ];

    /**
     * The current locale.
     *
     * @var string
     */
    public $currentLocale;

    /**
     * Mount the component.
     *
     * @return void
     */
    public function mount()
    {
        $this->currentLocale = App::getLocale();
    }

    /**
     * Render the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.language-switcher');
    }
}
