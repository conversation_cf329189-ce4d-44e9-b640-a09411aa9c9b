<?php

namespace App\Models;

use App\Models\FieldRate;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Field extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'description',
        'opening_time',
        'closing_time',
        'contact_employee_id',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'opening_time' => 'datetime:H:i',
        'closing_time' => 'datetime:H:i',
        'is_active' => 'boolean',
    ];

    /**
     * Get the rate for this field
     */
    public function rate()
    {
        return $this->hasOne(FieldRate::class);
    }

    /**
     * Get the reservations for this field
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the contact employee for this field
     */
    public function contactEmployee()
    {
        return $this->belongsTo(User::class, 'contact_employee_id');
    }

    /**
     * Check if the field is available for a given date and time range
     */
    public function isAvailable($date, $startTime, $endTime)
    {
        return $this->reservations()
            ->where('date', $date)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    $q->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime);
                });
            })
            ->where('status', '!=', 'cancelled')
            ->count() === 0;
    }
}
