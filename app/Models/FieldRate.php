<?php

namespace App\Models;

use App\Models\Field;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FieldRate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_id',
        'day_rate',
        'night_rate',
        'night_rate_start_time',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'day_rate' => 'decimal:2',
        'night_rate' => 'decimal:2',
        'night_rate_start_time' => 'datetime:H:i',
    ];

    /**
     * Get the field that owns the rate
     */
    public function field()
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Calculate the rate for a given time
     */
    public function getRateForTime($time)
    {
        if (!$this->night_rate) {
            return $this->day_rate;
        }

        $timeObj = new \DateTime($time);
        $nightStartObj = new \DateTime($this->night_rate_start_time);

        return $timeObj >= $nightStartObj ? $this->night_rate : $this->day_rate;
    }
}
