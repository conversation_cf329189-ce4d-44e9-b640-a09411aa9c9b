<?php

namespace App\Models;

use App\Models\Field;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reservation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'field_id',
        'date',
        'start_time',
        'end_time',
        'total_price',
        'status',
        'approved_by',
        'approved_at',
        'notes',
        'cancellation_deadline',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'start_time' => 'string',
        'end_time' => 'string',
        'total_price' => 'decimal:2',
        'approved_at' => 'datetime',
        'cancellation_deadline' => 'date',
    ];

    /**
     * Get the user that owns the reservation
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the field that is reserved
     */
    public function field()
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the employee who approved the reservation
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if the reservation can be cancelled
     */
    public function canBeCancelled()
    {
        if (!$this->cancellation_deadline) {
            return true;
        }

        return now()->lessThan($this->cancellation_deadline);
    }

    /**
     * Calculate the duration in hours
     */
    public function getDurationInHours()
    {
        $start = new \DateTime($this->start_time);
        $end = new \DateTime($this->end_time);
        $interval = $start->diff($end);

        return $interval->h + ($interval->i / 60);
    }

    /**
     * Scope a query to only include pending reservations
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved reservations
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include active reservations (not cancelled or rejected)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'approved']);
    }
}
