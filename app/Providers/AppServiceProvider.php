<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->environment('local') && class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set the application locale from session
        if (session()->has('locale')) {
            app()->setLocale(session('locale'));
        }

        // Only run this check if the application is not in the console (i.e., not running migrations or seeders)
        // and if the users table exists
        if (!$this->app->runningInConsole() && Schema::hasTable('users')) {
            $this->ensureAdminUserExists();
        }
    }

    /**
     * Ensure at least one admin user exists in the system.
     */
    private function ensureAdminUserExists(): void
    {
        // Check if there's at least one admin user
        $adminExists = User::where('role', 'administrator')->exists();

        // If no admin user exists, create one
        if (!$adminExists) {
            User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('mitahanjabo'),
                'role' => 'administrator',
                'email_verified_at' => now(),
            ]);
        }
    }
}
