<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class EnsureAdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if there's at least one admin user
        $adminExists = User::where('role', 'administrator')->exists();

        // If no admin user exists, create one
        if (!$adminExists) {
            User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('einannosta'),
                'role' => 'administrator',
                'email_verified_at' => now(),
            ]);

            $this->command->info('Super Admin user created successfully!');
        } else {
            $this->command->info('Admin user already exists. No new admin user created.');
        }
    }
}
