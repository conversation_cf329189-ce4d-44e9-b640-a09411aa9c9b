<?php

namespace Database\Seeders;

use App\Models\Field;
use App\Models\FieldRate;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('einannosta'),
                'role' => 'administrator',
            ]
        );

        // Create employee user if it doesn't exist
        $employee = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Employee User',
                'password' => Hash::make('password'),
                'role' => 'employee',
            ]
        );

        // Create regular user if it doesn't exist
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => Hash::make('password'),
                'role' => 'user',
            ]
        );

        // Create soccer field if it doesn't exist
        $soccerField = Field::firstOrCreate(
            ['name' => 'Main Soccer Field', 'type' => 'soccer'],
            [
                'description' => 'Professional soccer field with natural grass.',
                'opening_time' => '08:00:00',
                'closing_time' => '22:00:00',
                'contact_employee_id' => $employee->id,
                'is_active' => true,
            ]
        );

        // Create multi-purpose field if it doesn't exist
        $multiPurposeField = Field::firstOrCreate(
            ['name' => 'Multi-Purpose Court', 'type' => 'multi-purpose'],
            [
                'description' => 'Versatile court suitable for basketball, volleyball, and other sports.',
                'opening_time' => '08:00:00',
                'closing_time' => '22:00:00',
                'contact_employee_id' => $employee->id,
                'is_active' => true,
            ]
        );

        // Create bolas field if it doesn't exist
        $bolasField = Field::firstOrCreate(
            ['name' => 'Bolas Court', 'type' => 'bolas'],
            [
                'description' => 'Dedicated court for playing bolas.',
                'opening_time' => '09:00:00',
                'closing_time' => '21:00:00',
                'contact_employee_id' => null,
                'is_active' => true,
            ]
        );

        // Create patio field if it doesn't exist
        $patioField = Field::firstOrCreate(
            ['name' => 'Event Patio', 'type' => 'patio'],
            [
                'description' => 'Covered patio area for events and gatherings.',
                'opening_time' => '10:00:00',
                'closing_time' => '23:00:00',
                'contact_employee_id' => $admin->id,
                'is_active' => true,
            ]
        );

        // Create field rates if they don't exist
        FieldRate::firstOrCreate(
            ['field_id' => $soccerField->id],
            [
                'day_rate' => 50.00,
                'night_rate' => 75.00,
                'night_rate_start_time' => '18:00:00',
            ]
        );

        FieldRate::firstOrCreate(
            ['field_id' => $multiPurposeField->id],
            [
                'day_rate' => 35.00,
                'night_rate' => 50.00,
                'night_rate_start_time' => '18:00:00',
            ]
        );

        FieldRate::firstOrCreate(
            ['field_id' => $bolasField->id],
            [
                'day_rate' => 25.00,
                'night_rate' => null,
                'night_rate_start_time' => '18:00:00',
            ]
        );

        FieldRate::firstOrCreate(
            ['field_id' => $patioField->id],
            [
                'day_rate' => 100.00,
                'night_rate' => 150.00,
                'night_rate_start_time' => '17:00:00',
            ]
        );
    }
}
