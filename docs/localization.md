# Localization System Documentation

This document provides information on how to use and extend the localization system in the Sport Park Marie Pampoen application.

## Supported Languages

The application supports the following languages:

1. English (en)
2. Spanish (es)
3. Dutch (nl)
4. Papiamentu (pap)

## How Localization Works

The application uses Lara<PERSON>'s built-in localization features. The system works as follows:

1. The user's language preference is stored in the session.
2. A middleware (`SetLocale`) checks for the language preference and sets the application locale accordingly.
3. Translation strings are stored in language files in the `lang/{locale}.json` files.
4. The application uses the `__()` helper function to retrieve translated strings.

## Language Switcher

A language switcher component is available throughout the application, allowing users to change their language preference. The language preference persists across sessions.

## Adding New Translated Strings

To add new translated strings to the application, follow these steps:

1. Open the language files in the `lang` directory:
   - `lang/en.json` (English)
   - `lang/es.json` (Spanish)
   - `lang/nl.json` (Dutch)
   - `lang/pap.json` (Papiamentu)

2. Add your new string key and value to each language file. For example:

```json
// lang/en.json
{
    // ... existing translations
    "New String": "This is a new string in English"
}

// lang/es.json
{
    // ... existing translations
    "New String": "Esta es una nueva cadena en español"
}

// lang/nl.json
{
    // ... existing translations
    "New String": "Dit is een nieuwe string in het Nederlands"
}

// lang/pap.json
{
    // ... existing translations
    "New String": "Esaki ta un string nobo na Papiamentu"
}
```

3. Use the translation string in your views or controllers:

```php
// In a Blade view
{{ __('New String') }}

// In a controller or other PHP file
$translatedString = __('New String');
```

## Best Practices

1. **Organize translations logically**: Group related translations together in the language files and use comments to separate sections.

2. **Use descriptive keys**: Choose translation keys that clearly describe the content they represent.

3. **Keep translations consistent**: Ensure that the same concepts use the same terminology across all translations.

4. **Test all languages**: When adding new features, test the application in all supported languages to ensure everything displays correctly.

5. **Handle pluralization**: For strings that change based on count, use Laravel's pluralization features:

```php
// In language files
'apples' => '{0} No apples|{1} One apple|[2,*] :count apples',

// In code
{{ trans_choice('messages.apples', $count) }}
```

6. **Parameters**: For strings with variable parts, use parameters:

```php
// In language files
'welcome' => 'Welcome, :name!',

// In code
{{ __('messages.welcome', ['name' => $user->name]) }}
```

## Fallback Language

If a translation string is not available in the user's selected language, the system will fall back to the default language (English). This is configured in `config/app.php` with the `fallback_locale` setting.

## Adding a New Language

To add support for a new language:

1. Create a new language file in the `lang` directory (e.g., `lang/fr.json` for French).
2. Add all the translation strings to this file.
3. Update the `$languages` array in the `app/Http/Middleware/SetLocale.php` file.
4. Update the `$languages` array in the `app/Livewire/LanguageSwitcher.php` file.
5. Add the new language flag to the `$flagMap` array in the `resources/views/components/flag.blade.php` file.
