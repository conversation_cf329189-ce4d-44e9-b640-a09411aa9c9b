<x-layouts.app title="Admin: Edit Field">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Edit Field: {{ $field->name }}</h3>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.fields.show', $field) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Field
                            </a>
                            <a href="{{ route('admin.fields.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                All Fields
                            </a>
                        </div>
                    </div>

                    @if($errors->any())
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <strong class="font-bold">Error!</strong>
                            <ul>
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.fields.update', $field) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Field Name</label>
                                <input type="text" name="name" id="name" value="{{ old('name', $field->name) }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                            </div>

                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700">Field Type</label>
                                <select id="type" name="type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                    <option value="">Select a type</option>
                                    <option value="soccer" {{ old('type', $field->type) == 'soccer' ? 'selected' : '' }}>Soccer</option>
                                    <option value="multi-purpose" {{ old('type', $field->type) == 'multi-purpose' ? 'selected' : '' }}>Multi-purpose</option>
                                    <option value="bolas" {{ old('type', $field->type) == 'bolas' ? 'selected' : '' }}>Bolas</option>
                                    <option value="patio" {{ old('type', $field->type) == 'patio' ? 'selected' : '' }}>Patio</option>
                                </select>
                            </div>

                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea id="description" name="description" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ old('description', $field->description) }}</textarea>
                            </div>

                            <div>
                                <label for="opening_time" class="block text-sm font-medium text-gray-700">Opening Time</label>
                                <input type="time" name="opening_time" id="opening_time" value="{{ old('opening_time', \Carbon\Carbon::parse($field->opening_time)->format('H:i')) }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                            </div>

                            <div>
                                <label for="closing_time" class="block text-sm font-medium text-gray-700">Closing Time</label>
                                <input type="time" name="closing_time" id="closing_time" value="{{ old('closing_time', \Carbon\Carbon::parse($field->closing_time)->format('H:i')) }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                            </div>

                            <div>
                                <label for="contact_employee_id" class="block text-sm font-medium text-gray-700">Contact Employee</label>
                                <select id="contact_employee_id" name="contact_employee_id" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">None</option>
                                    @foreach($employees as $employee)
                                        <option value="{{ $employee->id }}" {{ old('contact_employee_id', $field->contact_employee_id) == $employee->id ? 'selected' : '' }}>
                                            {{ $employee->name }} ({{ ucfirst($employee->role) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div>
                                <div class="flex items-center">
                                    <input id="is_active" name="is_active" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" {{ old('is_active', $field->is_active) ? 'checked' : '' }}>
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                        Active (available for reservations)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <h4 class="text-lg font-semibold mb-4">Pricing Information</h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="day_rate" class="block text-sm font-medium text-gray-700">Day Rate (per hour)</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">$</span>
                                        </div>
                                        <input type="number" name="day_rate" id="day_rate" min="0" step="0.01" value="{{ old('day_rate', $field->rate ? $field->rate->day_rate : '') }}" class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md" placeholder="0.00" required>
                                    </div>
                                </div>

                                <div>
                                    <label for="night_rate" class="block text-sm font-medium text-gray-700">Night Rate (per hour, optional)</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">$</span>
                                        </div>
                                        <input type="number" name="night_rate" id="night_rate" min="0" step="0.01" value="{{ old('night_rate', $field->rate ? $field->rate->night_rate : '') }}" class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md" placeholder="0.00">
                                    </div>
                                </div>

                                <div>
                                    <label for="night_rate_start_time" class="block text-sm font-medium text-gray-700">Night Rate Start Time</label>
                                    <input type="time" name="night_rate_start_time" id="night_rate_start_time" value="{{ old('night_rate_start_time', $field->rate ? \Carbon\Carbon::parse($field->rate->night_rate_start_time)->format('H:i') : '18:00') }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                Update Field
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
