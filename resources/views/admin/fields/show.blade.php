<x-layouts.app title="Admin: Field Details">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-2xl font-semibold">{{ $field->name }}</h3>
                            <p class="text-gray-600 capitalize">{{ str_replace('-', ' ', $field->type) }}</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.fields.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Fields
                            </a>
                            <a href="{{ route('admin.fields.edit', $field) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                Edit Field
                            </a>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <div class="mt-6 bg-gray-50 p-4 rounded-md">
                        <div class="flex items-center">
                            <div class="mr-4">
                                <span class="inline-flex items-center justify-center h-10 w-10 rounded-full {{ $field->is_active ? 'bg-green-100' : 'bg-red-100' }}">
                                    <span class="text-lg font-medium leading-none {{ $field->is_active ? 'text-green-800' : 'text-red-800' }}">
                                        {{ $field->is_active ? 'A' : 'I' }}
                                    </span>
                                </span>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium">
                                    {{ $field->is_active ? 'Active' : 'Inactive' }}
                                </h4>
                                <p class="text-sm text-gray-600">
                                    {{ $field->is_active ? 'This field is available for reservations.' : 'This field is not available for reservations.' }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-lg font-semibold mb-2">Field Details</h4>
                            <div class="bg-white rounded-md border border-gray-200 p-4">
                                @if($field->description)
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-600">Description</p>
                                        <p class="mt-1">{{ $field->description }}</p>
                                    </div>
                                @endif

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Opening Hours</p>
                                        <p class="font-medium">{{ \Carbon\Carbon::parse($field->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($field->closing_time)->format('H:i') }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-600">Contact Employee</p>
                                        <p class="font-medium">{{ $field->contactEmployee ? $field->contactEmployee->name : 'None assigned' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold mb-2">Pricing Information</h4>
                            <div class="bg-white rounded-md border border-gray-200 p-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Day Rate</p>
                                        <p class="font-medium">${{ $field->rate ? number_format($field->rate->day_rate, 2) : 'N/A' }}/hour</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-600">Night Rate</p>
                                        <p class="font-medium">
                                            @if($field->rate && $field->rate->night_rate)
                                                ${{ number_format($field->rate->night_rate, 2) }}/hour
                                                <span class="text-xs text-gray-500">(after {{ \Carbon\Carbon::parse($field->rate->night_rate_start_time)->format('H:i') }})</span>
                                            @else
                                                Not set
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold mb-2">Upcoming Reservations</h4>

                        @if($field->reservations->isEmpty())
                            <p class="text-gray-500">No upcoming reservations for this field.</p>
                        @else
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($field->reservations as $reservation)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ $reservation->user->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $reservation->date->format('M d, Y') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ \Carbon\Carbon::parse($reservation->start_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($reservation->end_time)->format('H:i') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    @if($reservation->status === 'pending')
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                            Pending
                                                        </span>
                                                    @elseif($reservation->status === 'approved')
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                            Approved
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    ${{ number_format($reservation->total_price, 2) }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div class="flex space-x-2">
                                                        <a href="{{ route('admin.reservations.show', $reservation) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                                        <a href="{{ route('admin.reservations.edit', $reservation) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>

                                                        @if($reservation->status === 'pending')
                                                            <form action="{{ route('admin.reservations.approve', $reservation) }}" method="POST" class="inline">
                                                                @csrf
                                                                <button type="submit" class="text-green-600 hover:text-green-900">Approve</button>
                                                            </form>

                                                            <form action="{{ route('admin.reservations.reject', $reservation) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to reject this reservation?');">
                                                                @csrf
                                                                <button type="submit" class="text-red-600 hover:text-red-900">Reject</button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
