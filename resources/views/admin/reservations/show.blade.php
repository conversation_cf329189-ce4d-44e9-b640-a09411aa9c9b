<x-layouts.app title="Admin: Reservation Details">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Reservation #{{ $reservation->id }}</h3>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.reservations.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Reservations
                            </a>
                            <a href="{{ route('admin.reservations.edit', $reservation) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                Edit Reservation
                            </a>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    <div class="bg-gray-50 p-4 rounded-md mb-6">
                        <div class="flex items-center">
                            <div class="mr-4">
                                <span class="inline-flex items-center justify-center h-10 w-10 rounded-full
                                    @if($reservation->status === 'pending') bg-yellow-100
                                    @elseif($reservation->status === 'approved') bg-green-100
                                    @elseif($reservation->status === 'rejected') bg-red-100
                                    @elseif($reservation->status === 'cancelled') bg-gray-100
                                    @endif">
                                    <span class="text-lg font-medium leading-none
                                        @if($reservation->status === 'pending') text-yellow-800
                                        @elseif($reservation->status === 'approved') text-green-800
                                        @elseif($reservation->status === 'rejected') text-red-800
                                        @elseif($reservation->status === 'cancelled') text-gray-800
                                        @endif">
                                        {{ $reservation->status[0] }}
                                    </span>
                                </span>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium capitalize">{{ $reservation->status }}</h4>
                                <p class="text-sm text-gray-600">
                                    @if($reservation->status === 'pending')
                                        This reservation is waiting for approval.
                                    @elseif($reservation->status === 'approved')
                                        Approved by {{ $reservation->approvedBy->name ?? 'an employee' }} on {{ $reservation->approved_at->format('M d, Y H:i') }}.
                                    @elseif($reservation->status === 'rejected')
                                        This reservation has been rejected.
                                    @elseif($reservation->status === 'cancelled')
                                        This reservation has been cancelled.
                                    @endif
                                </p>
                            </div>
                            <div class="ml-auto">
                                @if($reservation->status === 'pending')
                                    <div class="flex space-x-2">
                                        <form action="{{ route('admin.reservations.approve', $reservation) }}" method="POST">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                                Approve
                                            </button>
                                        </form>

                                        <form action="{{ route('admin.reservations.reject', $reservation) }}" method="POST" onsubmit="return confirm('Are you sure you want to reject this reservation?');">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-800 focus:outline-none focus:border-red-800 focus:ring ring-red-300 disabled:opacity-25 transition">
                                                Reject
                                            </button>
                                        </form>
                                    </div>
                                @endif

                                @if($reservation->status !== 'cancelled')
                                    <form action="{{ route('admin.reservations.destroy', $reservation) }}" method="POST" class="mt-2" onsubmit="return confirm('Are you sure you want to cancel this reservation?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                            Cancel Reservation
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-md font-semibold mb-2">Reservation Details</h4>
                            <div class="bg-white rounded-md border border-gray-200 p-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">User</p>
                                        <p class="font-medium">{{ $reservation->user->name }}</p>
                                        <p class="text-xs text-gray-500">{{ $reservation->user->email }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Field</p>
                                        <p class="font-medium">{{ $reservation->field->name }}</p>
                                        <p class="text-xs text-gray-500 capitalize">{{ str_replace('-', ' ', $reservation->field->type) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Date</p>
                                        <p class="font-medium">{{ $reservation->date->format('F j, Y') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Time</p>
                                        <p class="font-medium">{{ \Carbon\Carbon::parse($reservation->start_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($reservation->end_time)->format('H:i') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Duration</p>
                                        <p class="font-medium">{{ $reservation->getDurationInHours() }} hours</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Total Price</p>
                                        <p class="font-medium">${{ number_format($reservation->total_price, 2) }}</p>
                                    </div>
                                </div>

                                @if($reservation->notes)
                                    <div class="mt-4">
                                        <p class="text-sm text-gray-600">Notes</p>
                                        <p class="mt-1">{{ $reservation->notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-semibold mb-2">Additional Information</h4>
                            <div class="bg-white rounded-md border border-gray-200 p-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Reservation Created</p>
                                        <p class="font-medium">{{ $reservation->created_at->format('M d, Y H:i') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Last Updated</p>
                                        <p class="font-medium">{{ $reservation->updated_at->format('M d, Y H:i') }}</p>
                                    </div>

                                    @if($reservation->approved_at)
                                        <div>
                                            <p class="text-sm text-gray-600">Approved By</p>
                                            <p class="font-medium">{{ $reservation->approvedBy->name ?? 'Unknown' }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-600">Approved At</p>
                                            <p class="font-medium">{{ $reservation->approved_at->format('M d, Y H:i') }}</p>
                                        </div>
                                    @endif

                                    @if($reservation->cancellation_deadline)
                                        <div class="col-span-2">
                                            <p class="text-sm text-gray-600">Cancellation Deadline</p>
                                            <p class="font-medium">{{ $reservation->cancellation_deadline->format('M d, Y') }}</p>
                                            <p class="text-xs text-gray-500 mt-1">
                                                @if($reservation->canBeCancelled())
                                                    The user can cancel or modify this reservation until this date.
                                                @else
                                                    The cancellation deadline has passed. The user can no longer cancel or modify this reservation.
                                                @endif
                                            </p>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="mt-4">
                                <h4 class="text-md font-semibold mb-2">Field Information</h4>
                                <div class="bg-white rounded-md border border-gray-200 p-4">
                                    <p class="text-sm text-gray-600">Operating Hours</p>
                                    <p class="font-medium">{{ \Carbon\Carbon::parse($reservation->field->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($reservation->field->closing_time)->format('H:i') }}</p>

                                    @if($reservation->field->contactEmployee)
                                        <div class="mt-2">
                                            <p class="text-sm text-gray-600">Contact Person</p>
                                            <p class="font-medium">{{ $reservation->field->contactEmployee->name }}</p>
                                        </div>
                                    @endif

                                    <div class="mt-4">
                                        <a href="{{ route('admin.fields.show', $reservation->field) }}" class="text-blue-600 hover:text-blue-900">View Field Details</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
