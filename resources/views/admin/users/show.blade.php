<x-layouts.app title="Admin: User Details">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">User Details</h3>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.users.edit', $user) }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                                Edit User
                            </a>
                            <a href="{{ route('admin.users.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-zinc-700 border border-transparent rounded-md font-semibold text-xs text-gray-700 dark:text-white uppercase tracking-widest hover:bg-gray-300 dark:hover:bg-zinc-600 active:bg-gray-400 dark:active:bg-zinc-500 focus:outline-none focus:border-gray-400 dark:focus:border-zinc-500 focus:ring ring-gray-300 dark:ring-zinc-400 disabled:opacity-25 transition">
                                Back to List
                            </a>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="mb-4 px-4 py-2 bg-green-100 border border-green-200 text-green-700 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-sm">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">User Information</h4>
                                
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Name</p>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->name }}</p>
                                </div>
                                
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Email</p>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->email }}</p>
                                </div>
                                
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Role</p>
                                    <p class="text-lg font-medium">
                                        @if($user->role === 'administrator')
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                Administrator
                                            </span>
                                        @elseif($user->role === 'employee')
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                Employee
                                            </span>
                                        @else
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                User
                                            </span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Account Details</h4>
                                
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Created</p>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->created_at->format('F j, Y, g:i a') }}</p>
                                </div>
                                
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->updated_at->format('F j, Y, g:i a') }}</p>
                                </div>
                                
                                @if($user->email_verified_at)
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Email Verified</p>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->email_verified_at->format('F j, Y, g:i a') }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        @if(auth()->id() !== $user->id)
                        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-zinc-700">
                            <form action="{{ route('admin.users.destroy', $user) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-800 focus:outline-none focus:border-red-800 focus:ring ring-red-300 disabled:opacity-25 transition">
                                    Delete User
                                </button>
                            </form>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
