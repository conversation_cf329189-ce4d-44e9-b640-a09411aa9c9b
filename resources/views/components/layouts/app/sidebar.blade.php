<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <flux:sidebar sticky stashable class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route('dashboard') }}" class="me-5 flex items-center space-x-2 rtl:space-x-reverse" wire:navigate>
                <x-app-logo />
            </a>

            <!-- Language Switcher -->
            <div class="px-3 py-2">
                <livewire:language-switcher />
            </div>

            <flux:navlist variant="outline">
                <flux:navlist.group :heading="__('Platform')" class="grid">
                    <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>{{ __('Home') }}</flux:navlist.item>
                    <flux:navlist.item icon="squares-2x2" :href="route('fields.index')" :current="request()->routeIs('fields.*') && !request()->is('calendar') && !request()->is('calendar/*')" wire:navigate>{{ __('Fields') }}</flux:navlist.item>
                    <flux:navlist.item icon="calendar" :href="route('fields.calendar')" :current="request()->is('calendar') || request()->is('calendar/*')" wire:navigate.prevent>{{ __('Calendar') }}</flux:navlist.item>
                    <flux:navlist.item icon="clipboard-document-list" :href="route('reservations.index')" :current="request()->routeIs('reservations.*')" wire:navigate>{{ __('My Reservations') }}</flux:navlist.item>
                </flux:navlist.group>
            </flux:navlist>

            @if(auth()->user()->isEmployee())
            <flux:navlist variant="outline">
                <flux:navlist.group :heading="__('Administration')" class="grid">
                    <flux:navlist.item icon="building-office-2" :href="route('admin.fields.index')" :current="request()->routeIs('admin.fields.*')" wire:navigate>{{ __('Manage Fields') }}</flux:navlist.item>
                    <flux:navlist.item icon="clipboard-document-check" :href="route('admin.reservations.index')" :current="request()->routeIs('admin.reservations.*')" wire:navigate>{{ __('Manage Reservations') }}</flux:navlist.item>
                    @if(auth()->user()->isAdmin())
                    <flux:navlist.item icon="users" :href="route('admin.users.index')" :current="request()->routeIs('admin.users.*')" wire:navigate>{{ __('Manage Users') }}</flux:navlist.item>
                    @endif
                </flux:navlist.group>
            </flux:navlist>
            @endif

            <flux:spacer />

            <!-- Desktop User Menu -->
            <flux:dropdown position="bottom" align="start">
                <flux:profile
                    :name="auth()->user()->name"
                    :initials="auth()->user()->initials()"
                    icon-trailing="chevrons-up-down"
                />

                <flux:menu class="w-[220px]">
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        {{ auth()->user()->initials() }}
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Mobile User Menu -->
        <flux:header class="lg:hidden">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <flux:spacer />

            <flux:dropdown position="top" align="end">
                <flux:profile
                    :initials="auth()->user()->initials()"
                    icon-trailing="chevron-down"
                />

                <flux:menu>
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        {{ auth()->user()->initials() }}
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('dashboard')" icon="home" :current="request()->routeIs('dashboard')" wire:navigate>{{ __('Home') }}</flux:menu.item>
                        <flux:menu.item :href="route('fields.index')" icon="squares-2x2" wire:navigate>{{ __('Fields') }}</flux:menu.item>
                        <flux:menu.item :href="route('fields.calendar')" icon="calendar" :current="request()->is('calendar') || request()->is('calendar/*')" wire:navigate.prevent>{{ __('Calendar') }}</flux:menu.item>
                        <flux:menu.item :href="route('reservations.index')" icon="clipboard-document-list" wire:navigate>{{ __('My Reservations') }}</flux:menu.item>
                    </flux:menu.radio.group>

                    @if(auth()->user()->isEmployee())
                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('admin.fields.index')" icon="building-office-2" wire:navigate>{{ __('Manage Fields') }}</flux:menu.item>
                        <flux:menu.item :href="route('admin.reservations.index')" icon="clipboard-document-check" wire:navigate>{{ __('Manage Reservations') }}</flux:menu.item>
                        @if(auth()->user()->isAdmin())
                        <flux:menu.item :href="route('admin.users.index')" icon="users" wire:navigate>{{ __('Manage Users') }}</flux:menu.item>
                        @endif
                    </flux:menu.radio.group>
                    @endif

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        {{ $slot }}

        @fluxScripts

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Handle calendar navigation
                const calendarLinks = document.querySelectorAll('[href="{{ route('fields.calendar') }}"]');

                calendarLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        window.location.href = "{{ route('fields.calendar') }}";
                    });
                });
            });
        </script>
    </body>
</html>
