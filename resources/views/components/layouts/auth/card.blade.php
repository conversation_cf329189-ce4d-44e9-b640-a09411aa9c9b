<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
        <style>
            /* Background image and parallax effect */
            .bg-image-container {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                overflow: hidden;
                will-change: transform;
            }

            .bg-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
                display: block;
                transform: translateZ(0);
                transition: transform 0.3s ease-out;
                will-change: transform;
            }

            .bg-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
            }

            /* Auth card styles */
            .auth-container {
                position: relative;
                z-index: 1;
                display: flex;
                min-height: 100vh;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 1.5rem;
                padding: 1.5rem;
            }

            .auth-card {
                width: 100%;
                max-width: 28rem;
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 0.75rem;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                overflow: hidden;
                backdrop-filter: blur(10px);
            }

            .auth-card-content {
                padding: 2rem 2.5rem;
            }

            .logo-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1.5rem;
            }

            .logo-image {
                height: 3rem;
                width: auto;
                max-width: 180px;
                object-fit: contain;
                background-color: white;
                padding: 0.5rem;
                border-radius: 0.375rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }

            .dark .auth-card {
                background-color: rgba(28, 25, 23, 0.9);
                border: 1px solid rgba(68, 64, 60, 0.5);
            }

            @media (min-width: 768px) {
                .auth-container {
                    padding: 2.5rem;
                }
            }

            @media (max-width: 640px) {
                .logo-image {
                    height: 2.5rem;
                    max-width: 150px;
                    padding: 0.375rem;
                }
            }
        </style>
    </head>
    <body class="min-h-screen antialiased">
        <!-- Background Image with Parallax Effect -->
        <div class="bg-image-container">
            <img src="{{ asset('images/soccer-field-ocean.jpg') }}" alt="Soccer Field Next to Ocean" class="bg-image">
            <div class="bg-overlay"></div>
        </div>

        <div class="auth-container">
            <div class="flex w-full max-w-md flex-col gap-6">
                <a href="{{ route('home') }}" class="logo-container" wire:navigate>
                    <img src="{{ asset('images/sport-park-marie-pampoen.png') }}" alt="Sport Park Marie Pampoen Logo" class="logo-image">
                    <span class="sr-only">Sport Park Marie Pampoen</span>
                </a>

                <div class="flex flex-col gap-6">
                    <div class="auth-card">
                        <div class="auth-card-content">{{ $slot }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Parallax Effect Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const bgImage = document.querySelector('.bg-image');
                const container = document.querySelector('body');

                if (bgImage && container) {
                    // Parallax factor (smaller = more subtle)
                    const parallaxFactor = 0.005; // Extremely subtle movement

                    // Get container dimensions
                    const containerWidth = container.clientWidth;
                    const containerHeight = container.clientHeight;

                    // Initial position (center)
                    let centerX = containerWidth / 2;
                    let centerY = containerHeight / 2;

                    // Update on mouse movement
                    document.addEventListener('mousemove', function(e) {
                        // Calculate mouse position relative to center
                        const mouseX = e.clientX;
                        const mouseY = e.clientY;

                        // Calculate offset from center (as percentage of container size)
                        const offsetX = (mouseX - centerX) / containerWidth;
                        const offsetY = (mouseY - centerY) / containerHeight;

                        // Apply parallax effect with subtle movement
                        // Use requestAnimationFrame for smoother performance
                        requestAnimationFrame(function() {
                            // Limit the movement to a few pixels for subtlety
                            const translateX = -offsetX * containerWidth * parallaxFactor;
                            const translateY = -offsetY * containerHeight * parallaxFactor;

                            bgImage.style.transform = `translate3d(${translateX}px, ${translateY}px, 0)`;
                        });
                    });

                    // Reset on window resize
                    window.addEventListener('resize', function() {
                        const newContainerWidth = container.clientWidth;
                        const newContainerHeight = container.clientHeight;
                        centerX = newContainerWidth / 2;
                        centerY = newContainerHeight / 2;
                    });
                }
            });
        </script>
        @fluxScripts
    </body>
</html>
