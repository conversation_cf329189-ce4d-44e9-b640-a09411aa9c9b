<x-layouts.app title="Dashboard">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            <div class="mb-6">
                <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Welcome to Sport Park Marie Pampoen</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            This is the reservation system for Sport Park Marie Pampoen. Here you can reserve various fields including soccer fields, multi-purpose fields, bolas fields, and patios.
                        </p>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            To get started, browse the available fields and check their availability. Once you find a suitable time slot, you can create a reservation. Your reservation will be pending until it's approved by an employee.
                        </p>
                        <p class="text-gray-600 dark:text-gray-300">
                            If you have any questions, please contact the park administration.
                        </p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Fields Card -->
                <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Fields</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Browse available fields and check their availability.</p>
                        <div class="flex space-x-2">
                            <a href="{{ route('fields.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                View Fields
                            </a>
                            <a href="{{ route('fields.calendar') }}" class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-600 border border-indigo-300 dark:border-indigo-500 rounded-md font-semibold text-xs text-indigo-800 dark:text-white uppercase tracking-widest hover:bg-indigo-200 dark:hover:bg-indigo-700 active:bg-indigo-300 dark:active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                                Calendar View
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Reservations Card -->
                <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">My Reservations</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">View, manage, and create new reservations.</p>
                        <div class="flex space-x-2">
                            <a href="{{ route('reservations.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                My Reservations
                            </a>
                            <a href="{{ route('reservations.create') }}" class="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-600 border border-green-300 dark:border-green-500 rounded-md font-semibold text-xs text-green-800 dark:text-white uppercase tracking-widest hover:bg-green-200 dark:hover:bg-green-700 active:bg-green-300 dark:active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                New Reservation
                            </a>
                        </div>
                    </div>
                </div>

                @if(auth()->user()->isEmployee())
                <!-- Admin Card -->
                <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Admin Panel</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Manage fields and reservations as an administrator.</p>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.fields.index') }}" class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-600 border border-indigo-300 dark:border-indigo-500 rounded-md font-semibold text-xs text-indigo-800 dark:text-white uppercase tracking-widest hover:bg-indigo-200 dark:hover:bg-indigo-700 active:bg-indigo-300 dark:active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                                Manage Fields
                            </a>
                            <a href="{{ route('admin.reservations.index') }}" class="inline-flex items-center px-4 py-2 bg-purple-100 dark:bg-purple-600 border border-purple-300 dark:border-purple-500 rounded-md font-semibold text-xs text-purple-800 dark:text-white uppercase tracking-widest hover:bg-purple-200 dark:hover:bg-purple-700 active:bg-purple-300 dark:active:bg-purple-800 focus:outline-none focus:border-purple-800 focus:ring ring-purple-300 disabled:opacity-25 transition">
                                Manage Reservations
                            </a>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</x-layouts.app>
