<x-layouts.app :title="$field->name . ' - Availability'">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-white">{{ $field->name }} - Availability</h3>
                            <p class="text-gray-600 dark:text-gray-300">Date: {{ \Carbon\Carbon::parse($date)->format('F j, Y') }}</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('fields.show', $field) }}" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md font-semibold text-xs text-gray-800 dark:text-white uppercase tracking-widest hover:bg-gray-200 dark:hover:bg-gray-700 active:bg-gray-300 dark:active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Field
                            </a>
                            <a href="{{ route('fields.calendar', $field->id) }}" class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-600 border border-indigo-300 dark:border-indigo-500 rounded-md font-semibold text-xs text-indigo-800 dark:text-white uppercase tracking-widest hover:bg-indigo-200 dark:hover:bg-indigo-700 active:bg-indigo-300 dark:active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                                View Calendar
                            </a>
                            <a href="{{ route('reservations.create', ['field_id' => $field->id, 'date' => $date]) }}" class="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-600 border border-green-300 dark:border-green-500 rounded-md font-semibold text-xs text-green-800 dark:text-white uppercase tracking-widest hover:bg-green-200 dark:hover:bg-green-700 active:bg-green-300 dark:active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                Make Reservation
                            </a>
                        </div>
                    </div>

                    <div class="mt-6">
                        <form action="{{ route('fields.availability', $field) }}" method="GET" class="flex items-end space-x-4">
                            <div>
                                <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Change Date</label>
                                <input type="date" name="date" id="date" min="{{ date('Y-m-d') }}" value="{{ $date }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-gray-900 dark:text-white rounded-md">
                            </div>
                            <div>
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                    Check Availability
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Operating Hours</h4>
                        <p class="mt-1 text-gray-700 dark:text-gray-300">{{ \Carbon\Carbon::parse($field->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($field->closing_time)->format('H:i') }}</p>
                    </div>

                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Existing Reservations</h4>

                        @if($reservations->isEmpty())
                            <p class="mt-2 text-green-600 dark:text-green-400">No reservations for this date. The field is fully available!</p>
                        @else
                            <div class="mt-2 overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-zinc-800">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-zinc-900 divide-y divide-gray-200 dark:divide-gray-700">
                                        @foreach($reservations as $reservation)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                                    {{ \Carbon\Carbon::parse($reservation->start_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($reservation->end_time)->format('H:i') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                    @if($reservation->status === 'approved')
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300">
                                                            Approved
                                                        </span>
                                                    @elseif($reservation->status === 'pending')
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300">
                                                            Pending
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Make a Reservation</h4>
                        <p class="mt-1 text-gray-700 dark:text-gray-300">Choose a time slot that doesn't conflict with existing reservations.</p>
                        <div class="mt-4">
                            <a href="{{ route('reservations.create', ['field_id' => $field->id, 'date' => $date]) }}" class="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-600 border border-green-300 dark:border-green-500 rounded-md font-semibold text-xs text-green-800 dark:text-white uppercase tracking-widest hover:bg-green-200 dark:hover:bg-green-700 active:bg-green-300 dark:active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                Make Reservation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
