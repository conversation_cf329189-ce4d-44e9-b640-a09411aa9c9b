<x-layouts.app title="{{ $field ? $field->name . ' - Calendar' : 'Field Calendar' }}">
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white" id="calendar-title">{{ $field ? $field->name . ' - Calendar' : 'Field Calendar' }}</h3>
                        <div class="flex space-x-2">
                            <a href="{{ route('fields.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md font-semibold text-xs text-gray-800 dark:text-white uppercase tracking-widest hover:bg-gray-200 dark:hover:bg-gray-700 active:bg-gray-300 dark:active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Fields
                            </a>
                            @if($field)
                                <a href="{{ route('fields.calendar') }}" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                    View All Fields
                                </a>
                            @endif
                        </div>
                    </div>

                    @if(!$field)
                        <div class="mb-6">
                            <label for="field-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filter by Field</label>
                            <select id="field-select" class="mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">All Fields</option>
                                @foreach($fields as $f)
                                    <option value="{{ $f->id }}">{{ $f->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="bg-gray-50 dark:bg-zinc-800 p-4 rounded-md mb-6">
                        <div class="flex flex-wrap items-center gap-6">
                            <div class="flex items-center">
                                <div class="mr-3">
                                    <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-white dark:bg-green-900 border-2 border-green-500 dark:border-green-700 shadow-sm">
                                        <span class="h-3.5 w-3.5 rounded-full bg-green-500 dark:bg-green-400"></span>
                                    </span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">Approved Reservations</span>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="mr-3">
                                    <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-white dark:bg-yellow-900 border-2 border-yellow-500 dark:border-yellow-700 shadow-sm">
                                        <span class="h-3.5 w-3.5 rounded-full bg-yellow-500 dark:bg-yellow-400"></span>
                                    </span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200">Pending Reservations</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4 flex justify-between items-center">
                        <button id="load-events-btn" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                            Refresh Calendar
                        </button>
                        <div id="calendar-status" class="text-sm text-gray-600 dark:text-gray-400"></div>
                    </div>

                    <div id="calendar" class="mt-4"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const fieldSelect = document.getElementById('field-select');

            let fieldId = {{ $field ? $field->id : 'null' }};
            console.log('Initial fieldId:', fieldId);

            // Initialize the calendar first
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                timeZone: 'local',
                nowIndicator: true,
                navLinks: true,
                dayMaxEvents: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                slotMinTime: '06:00:00',
                slotMaxTime: '23:00:00',
                height: 'auto',
                allDaySlot: false,
                events: function(info, successCallback, failureCallback) {
                    console.log('Calendar date range:', info.startStr, 'to', info.endStr);
                    console.log('Current fieldId:', fieldId);

                    // Build the URL with field_id if it exists
                    let apiUrl = '{{ route('api.reservations') }}?start=' + info.startStr + '&end=' + info.endStr;
                    if (fieldId) {
                        apiUrl += '&field_id=' + fieldId;
                    }
                    console.log('Fetching events from URL:', apiUrl);

                    // Fetch calendar events with the field_id parameter
                    fetch(apiUrl)
                        .then(response => {
                            console.log('Calendar API response status:', response.status);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events:', data);

                            if (Array.isArray(data) && data.length > 0) {
                                console.log(`Found ${data.length} events`);
                                data.forEach((event, index) => {
                                    console.log(`Event ${index + 1}:`, event);
                                });

                                // Make sure events have the required properties
                                const validEvents = data.map(event => {
                                    // Ensure event has required properties
                                    return {
                                        ...event,
                                        allDay: event.allDay || false,
                                        editable: false,
                                        display: 'block'
                                    };
                                });

                                successCallback(validEvents);
                            } else {
                                console.log('No events found or invalid data format');
                                successCallback([]);
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching events:', error);
                            failureCallback(error);
                        });
                },
                eventDidMount: function(info) {
                    // Add tooltip with additional information
                    const tooltip = document.createElement('div');
                    tooltip.className = 'fc-tooltip';

                    // Check if dark mode is enabled
                    const isDarkMode = document.documentElement.classList.contains('dark');

                    tooltip.innerHTML = `
                        <div class="p-2 bg-white dark:bg-zinc-800 shadow-lg rounded-md border border-gray-200 dark:border-zinc-700 text-gray-800 dark:text-white">
                            <p><strong>Field:</strong> ${info.event.extendedProps.field_name}</p>
                            <p><strong>User:</strong> ${info.event.extendedProps.user_name}</p>
                            <p><strong>Status:</strong> ${info.event.extendedProps.status}</p>
                            <p><strong>Price:</strong> $${info.event.extendedProps.total_price}</p>
                        </div>
                    `;

                    info.el.addEventListener('mouseover', function() {
                        document.body.appendChild(tooltip);
                        const rect = info.el.getBoundingClientRect();
                        tooltip.style.position = 'absolute';
                        tooltip.style.top = rect.bottom + window.scrollY + 5 + 'px';
                        tooltip.style.left = rect.left + window.scrollX + 'px';
                        tooltip.style.zIndex = 1000;
                    });

                    info.el.addEventListener('mouseout', function() {
                        if (document.body.contains(tooltip)) {
                            document.body.removeChild(tooltip);
                        }
                    });
                }
            });

            // Initialize calendar with current date
            const today = new Date();
            console.log('Calendar initialized with date:', today.toISOString());

            // Set up the manual refresh button
            const loadEventsBtn = document.getElementById('load-events-btn');
            const calendarStatus = document.getElementById('calendar-status');

            loadEventsBtn.addEventListener('click', function() {
                calendarStatus.textContent = 'Loading events...';

                // Get the current view's date range
                const startDate = calendar.view.activeStart.toISOString();
                const endDate = calendar.view.activeEnd.toISOString();

                // Build the URL with field_id and date range
                let apiUrl = '{{ route('api.reservations') }}?start=' + startDate + '&end=' + endDate;
                if (fieldId) {
                    apiUrl += '&field_id=' + fieldId;
                }
                console.log('Manually refreshing events from URL:', apiUrl);

                // Fetch events directly
                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        console.log('Manually loaded events:', data);

                        // Clear existing events
                        calendar.getEvents().forEach(event => {
                            event.remove();
                        });

                        // Add the events to the calendar
                        if (Array.isArray(data) && data.length > 0) {
                            data.forEach(event => {
                                calendar.addEvent({
                                    id: event.id,
                                    title: event.title,
                                    start: event.start,
                                    end: event.end,
                                    backgroundColor: event.backgroundColor,
                                    borderColor: event.borderColor,
                                    textColor: event.textColor,
                                    url: event.url,
                                    allDay: false
                                });
                            });
                            calendarStatus.textContent = `Loaded ${data.length} events`;
                        } else {
                            calendarStatus.textContent = 'No events found';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading events:', error);
                        calendarStatus.textContent = 'Error loading events';
                    });
            });

            calendar.render();

            // Fix for when calendar is opened from side menu or direct URL access
            function parseUrlForFieldId() {
                if (window.location.pathname.includes('/calendar/')) {
                    const pathParts = window.location.pathname.split('/');
                    const potentialId = pathParts[pathParts.length - 1];
                    if (potentialId && !isNaN(potentialId)) {
                        return parseInt(potentialId);
                    }
                }
                return null;
            }

            // Check if we have a field ID in the URL
            const urlFieldId = parseUrlForFieldId();
            console.log('URL fieldId:', urlFieldId);
            if (fieldId === null && urlFieldId !== null) {
                fieldId = urlFieldId;
                console.log('Updated fieldId:', fieldId);

                // Update the title if we have a field ID from the URL
                if (fieldSelect) {
                    // Set the dropdown value
                    fieldSelect.value = fieldId;

                    // Update the title
                    const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
                    if (selectedOption) {
                        const calendarTitle = document.getElementById('calendar-title');
                        calendarTitle.textContent = selectedOption.text + ' - Calendar';
                    }

                    // Trigger a refetch of events
                    calendar.refetchEvents();
                }
            }

            // Handle field selection change
            if (fieldSelect) {
                // Set the initial value of the dropdown if fieldId is set
                if (fieldId !== null) {
                    fieldSelect.value = fieldId;
                }

                fieldSelect.addEventListener('change', function() {
                    // Update the fieldId variable
                    fieldId = this.value ? parseInt(this.value) : null;
                    console.log('Field selection changed to:', fieldId);

                    // Update title
                    const calendarTitle = document.getElementById('calendar-title');
                    let fieldName = 'All Fields';

                    if (fieldId) {
                        const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
                        fieldName = selectedOption.text;
                        calendarTitle.textContent = fieldName + ' - Calendar';
                        // Update URL without reloading the page
                        history.pushState({}, '', `{{ route('fields.calendar') }}/${fieldId}`);
                    } else {
                        calendarTitle.textContent = 'Field Calendar';
                        // Update URL without reloading the page
                        history.pushState({}, '', `{{ route('fields.calendar') }}`);
                    }

                    // Update the calendar status
                    calendarStatus.textContent = 'Filtering by ' + fieldName;

                    // Manually fetch events with the new field_id
                    const startDate = calendar.view.activeStart.toISOString();
                    const endDate = calendar.view.activeEnd.toISOString();

                    // Build the URL with field_id if it exists
                    let apiUrl = '{{ route('api.reservations') }}?start=' + startDate + '&end=' + endDate;
                    if (fieldId) {
                        apiUrl += '&field_id=' + fieldId;
                    }

                    console.log('Fetching events for field change from URL:', apiUrl);

                    // Show loading indicator
                    calendarStatus.textContent = 'Loading events...';

                    // Fetch events directly
                    fetch(apiUrl)
                        .then(response => response.json())
                        .then(data => {
                            console.log('Loaded events after field change:', data);

                            // Clear existing events
                            calendar.getEvents().forEach(event => {
                                event.remove();
                            });

                            // Add the events to the calendar
                            if (Array.isArray(data) && data.length > 0) {
                                data.forEach(event => {
                                    calendar.addEvent({
                                        id: event.id,
                                        title: event.title,
                                        start: event.start,
                                        end: event.end,
                                        backgroundColor: event.backgroundColor,
                                        borderColor: event.borderColor,
                                        textColor: event.textColor,
                                        url: event.url,
                                        allDay: false,
                                        extendedProps: event.extendedProps
                                    });
                                });
                                calendarStatus.textContent = `Showing ${data.length} events for ${fieldName}`;
                            } else {
                                calendarStatus.textContent = `No events found for ${fieldName}`;
                            }
                        })
                        .catch(error => {
                            console.error('Error loading events:', error);
                            calendarStatus.textContent = 'Error loading events';
                        });
                });
            }
        });
    </script>

    <style>
        .fc-event {
            cursor: pointer;
            border-width: 2px;
        }
        .fc-tooltip {
            font-size: 0.875rem;
        }

        /* Make events more visible */
        .fc-timegrid-event {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-width: 2px !important;
        }

        /* Ensure event text is readable in light mode */
        .fc-event-title, .fc-event-time {
            font-weight: 600 !important;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
        }

        /* Dark mode styles for FullCalendar */
        .dark .fc-theme-standard .fc-scrollgrid,
        .dark .fc-theme-standard td,
        .dark .fc-theme-standard th {
            border-color: #4b5563;
        }

        .dark .fc-col-header-cell {
            background-color: #374151;
        }

        .dark .fc-col-header-cell-cushion {
            color: #e5e7eb;
        }

        .dark .fc-daygrid-day-number,
        .dark .fc-daygrid-day-top {
            color: #e5e7eb;
        }

        .dark .fc-timegrid-slot-label-cushion,
        .dark .fc-timegrid-axis-cushion {
            color: #e5e7eb;
        }

        .dark .fc-day-today {
            background-color: rgba(59, 130, 246, 0.1) !important;
        }

        .dark .fc-button-primary {
            background-color: #4b5563;
            border-color: #6b7280;
        }

        .dark .fc-button-primary:hover {
            background-color: #374151;
        }

        .dark .fc-button-primary:not(:disabled).fc-button-active,
        .dark .fc-button-primary:not(:disabled):active {
            background-color: #1f2937;
            border-color: #374151;
        }

        .dark .fc-toolbar-title {
            color: #e5e7eb;
        }

        .dark .fc-timegrid-event-harness-inset .fc-timegrid-event,
        .dark .fc-timegrid-event.fc-event-mirror {
            box-shadow: 0 0 0 1px #1f2937;
        }
    </style>
</x-layouts.app>
