<x-layouts.app title="Fields">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Available Fields</h3>
                        <a href="{{ route('fields.calendar') }}" class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-600 border border-indigo-300 dark:border-indigo-500 rounded-md font-semibold text-xs text-indigo-800 dark:text-white uppercase tracking-widest hover:bg-indigo-200 dark:hover:bg-indigo-700 active:bg-indigo-300 dark:active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                            View Calendar
                        </a>
                    </div>

                    @if($fields->isEmpty())
                        <p class="text-gray-500 dark:text-gray-400">No fields available at the moment.</p>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($fields as $field)
                                <div class="border dark:border-zinc-700 rounded-lg p-4 hover:shadow-md transition bg-white dark:bg-zinc-800">
                                    <h4 class="text-lg font-semibold text-gray-800 dark:text-white">{{ $field->name }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 capitalize">{{ str_replace('-', ' ', $field->type) }}</p>

                                    @if($field->description)
                                        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">{{ Str::limit($field->description, 100) }}</p>
                                    @endif

                                    <div class="mt-3 text-sm text-gray-700 dark:text-gray-300">
                                        <p>Opening hours: {{ \Carbon\Carbon::parse($field->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($field->closing_time)->format('H:i') }}</p>

                                        @if($field->rate)
                                            <p>Rate: ${{ number_format($field->rate->day_rate, 2) }}/hour</p>
                                            @if($field->rate->night_rate)
                                                <p>Night rate: ${{ number_format($field->rate->night_rate, 2) }}/hour (after {{ \Carbon\Carbon::parse($field->rate->night_rate_start_time)->format('H:i') }})</p>
                                            @endif
                                        @endif
                                    </div>

                                    <div class="mt-4 flex space-x-2">
                                        <a href="{{ route('fields.show', $field) }}" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                            View Details
                                        </a>

                                        <form action="{{ route('fields.availability', $field) }}" method="GET" class="inline">
                                            <input type="hidden" name="date" value="{{ date('Y-m-d') }}">
                                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-600 border border-green-300 dark:border-green-500 rounded-md font-semibold text-xs text-green-800 dark:text-white uppercase tracking-widest hover:bg-green-200 dark:hover:bg-green-700 active:bg-green-300 dark:active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                                Check Availability
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
