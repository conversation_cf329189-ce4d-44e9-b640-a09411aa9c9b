<x-layouts.app :title="$field->name">

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-zinc-900 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-zinc-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-white">{{ $field->name }}</h3>
                            <p class="text-gray-600 dark:text-gray-300 capitalize">{{ str_replace('-', ' ', $field->type) }}</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('fields.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md font-semibold text-xs text-gray-800 dark:text-white uppercase tracking-widest hover:bg-gray-200 dark:hover:bg-gray-700 active:bg-gray-300 dark:active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition">
                                Back to Fields
                            </a>
                            <a href="{{ route('fields.calendar', $field->id) }}" class="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-600 border border-indigo-300 dark:border-indigo-500 rounded-md font-semibold text-xs text-indigo-800 dark:text-white uppercase tracking-widest hover:bg-indigo-200 dark:hover:bg-indigo-700 active:bg-indigo-300 dark:active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition">
                                View Calendar
                            </a>
                            <a href="{{ route('reservations.create', ['field_id' => $field->id]) }}" class="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-600 border border-green-300 dark:border-green-500 rounded-md font-semibold text-xs text-green-800 dark:text-white uppercase tracking-widest hover:bg-green-200 dark:hover:bg-green-700 active:bg-green-300 dark:active:bg-green-800 focus:outline-none focus:border-green-800 focus:ring ring-green-300 disabled:opacity-25 transition">
                                Make Reservation
                            </a>
                        </div>
                    </div>

                    @if($field->description)
                        <div class="mt-4">
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Description</h4>
                            <p class="mt-1 text-gray-700 dark:text-gray-300">{{ $field->description }}</p>
                        </div>
                    @endif

                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Details</h4>
                            <div class="mt-2 space-y-2 text-gray-700 dark:text-gray-300">
                                <p><span class="font-medium">Opening Hours:</span> {{ \Carbon\Carbon::parse($field->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($field->closing_time)->format('H:i') }}</p>

                                @if($field->contactEmployee)
                                    <p><span class="font-medium">Contact Person:</span> {{ $field->contactEmployee->name }}</p>
                                @endif
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Pricing</h4>
                            <div class="mt-2 space-y-2 text-gray-700 dark:text-gray-300">
                                @if($field->rate)
                                    <p><span class="font-medium">Day Rate:</span> ${{ number_format($field->rate->day_rate, 2) }}/hour</p>

                                    @if($field->rate->night_rate)
                                        <p><span class="font-medium">Night Rate:</span> ${{ number_format($field->rate->night_rate, 2) }}/hour (after {{ \Carbon\Carbon::parse($field->rate->night_rate_start_time)->format('H:i') }})</p>
                                    @endif
                                @else
                                    <p class="text-gray-500 dark:text-gray-400">Pricing information not available.</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Check Availability</h4>
                        <form action="{{ route('fields.availability', $field) }}" method="GET" class="mt-2 flex items-end space-x-4">
                            <div>
                                <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date</label>
                                <input type="date" name="date" id="date" min="{{ date('Y-m-d') }}" value="{{ date('Y-m-d') }}" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-gray-900 dark:text-white rounded-md">
                            </div>
                            <div>
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-600 border border-blue-300 dark:border-blue-500 rounded-md font-semibold text-xs text-blue-800 dark:text-white uppercase tracking-widest hover:bg-blue-200 dark:hover:bg-blue-700 active:bg-blue-300 dark:active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition">
                                    Check Availability
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
