<div>
    <flux:dropdown position="bottom" align="end">
        <flux:button variant="outline" size="sm" class="flex items-center gap-1">
            <span class="flex items-center">
                <x-flag :locale="$currentLocale" class="mr-1" />
                {{ __($languages[$currentLocale]) }}
            </span>
            <flux:icon name="chevron-down" class="h-4 w-4" />
        </flux:button>

        <flux:menu>
            <flux:menu.radio.group>
                @foreach($languages as $code => $name)
                    <flux:menu.item
                        onclick="changeLanguage('{{ $code }}')"
                        :active="$currentLocale === '{{ $code }}'"
                        class="cursor-pointer"
                    >
                        <div class="flex items-center">
                            <x-flag :locale="$code" class="mr-2" />
                            {{ __($name) }}
                        </div>
                    </flux:menu.item>
                @endforeach
            </flux:menu.radio.group>
        </flux:menu>
    </flux:dropdown>

    <!-- Hidden form for language switching -->
    <form id="language-form" method="POST" action="{{ route('set.language') }}" class="hidden">
        @csrf
        <input type="hidden" name="locale" id="selected-locale">
    </form>

    <script>
        function changeLanguage(locale) {
            // Set the locale in the hidden form
            document.getElementById('selected-locale').value = locale;
            // Submit the form
            document.getElementById('language-form').submit();
        }
    </script>
</div>
