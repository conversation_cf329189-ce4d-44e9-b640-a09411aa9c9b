<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Sport Park Marie Pampoen</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=montserrat:700,800|instrument-sans:400,500,600" rel="stylesheet" />

        <!-- Livewire Styles -->
        @livewireStyles

        <!-- Vite -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Flux Appearance -->
        @fluxAppearance

        <!-- Styles -->
        <style>
            /* Universal box-sizing */
            *, *::before, *::after {
                box-sizing: border-box;
            }

            /* Base styles */
            html, body {
                margin: 0;
                padding: 0;
                font-family: 'Instrument Sans', sans-serif;
                height: 100%;
                width: 100%;
                overflow-x: hidden;
                display: flex;
                flex-direction: column;
                color: white;
            }

            /* Background image and overlay */
            body {
                background-color: white;
            }

            .hero-section {
                position: relative;
                overflow: hidden;
                height: 100vh;
                min-height: 600px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .bg-image {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                overflow: hidden;
                will-change: transform;
            }

            .bg-image img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center 25%;
                display: block;
                transform: translateZ(0);
                transition: transform 0.3s ease-out;
                will-change: transform;
            }

            .overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
            }

            /* Header and navigation */
            header {
                width: 100%;
                z-index: 10;
                box-sizing: border-box;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .navbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 1200px;
                margin: 0 auto;
                width: 100%;
                padding: 0.875rem 1.5rem;
            }

            .navbar-brand {
                display: flex;
                align-items: center;
                text-decoration: none;
            }

            .logo-container {
                display: flex;
                align-items: center;
            }

            .logo-image {
                height: 2.5rem;
                width: auto;
                max-width: 50px;
                object-fit: contain;
                margin-right: 0.5rem;
            }

            .logo-text {
                font-weight: 700;
                color: #262626;
                font-size: 1rem;
                font-family: 'Montserrat', sans-serif;
                line-height: 1.2;
            }

            .navbar-nav {
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
            }

            .navbar-nav a {
                display: inline-block;
                padding: 0.5rem 1.25rem;
                border-radius: 0.375rem;
                font-size: 0.875rem;
                font-weight: 500;
                line-height: 1.5;
                text-decoration: none;
                color: #525252;
                border: 1px solid transparent;
                transition: all 0.2s ease;
            }

            .navbar-nav a:hover {
                color: #262626;
                background-color: rgba(0, 0, 0, 0.05);
            }

            .navbar-nav a.btn-outline {
                border-color: #262626;
                color: #262626;
            }

            .navbar-nav a.btn-outline:hover {
                background-color: #262626;
                color: white;
            }

            @media (max-width: 640px) {
                .navbar {
                    flex-direction: column;
                    padding: 0.75rem;
                }

                .navbar-brand {
                    margin-bottom: 0.5rem;
                }

                .logo-image {
                    height: 2rem;
                    max-width: 40px;
                }

                .logo-text {
                    font-size: 0.875rem;
                }
            }

            /* Main content */
            .main-content {
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                padding: 0 1rem;
                width: 100%;
                box-sizing: border-box;
                z-index: 1;
                position: relative;
            }

            .content-wrapper {
                max-width: 800px;
                width: 100%;
                padding: 2rem 0;
            }

            h1 {
                font-family: 'Montserrat', sans-serif;
                font-size: 4rem;
                font-weight: 800;
                margin-bottom: 1.5rem;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }

            h1 .highlight {
                color: white;
            }

            p {
                font-size: 1.25rem;
                margin-bottom: 2rem;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }

            .cta-button {
                display: inline-block;
                background-color: transparent;
                color: white;
                font-weight: 600;
                padding: 0.75rem 2rem;
                border-radius: 0.5rem;
                text-decoration: none;
                font-size: 1.125rem;
                transition: all 0.3s ease;
                border: 2px solid white;
            }

            .cta-button:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            /* Responsive styles */
            @media (max-width: 768px) {
                h1 {
                    font-size: 3rem;
                }

                p {
                    font-size: 1rem;
                }
            }

            @media (max-width: 480px) {
                h1 {
                    font-size: 2.5rem;
                }
            }

            /* Additional content styles */
            .additional-content {
                background-color: white;
                color: #262626;
                padding: 4rem 1.5rem;
                position: relative;
                z-index: 1;
            }

            .content-section {
                max-width: 1200px;
                margin: 0 auto 3rem;
                padding: 0 1rem;
                text-align: center;
            }

            .content-section:last-child {
                margin-bottom: 0;
            }

            .content-section h2 {
                font-family: 'Montserrat', sans-serif;
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 1.5rem;
                color: #262626;
                text-align: center;
            }

            .content-section p {
                color: #525252;
                font-size: 1.125rem;
                line-height: 1.6;
                margin-bottom: 1.5rem;
                text-shadow: none;
                max-width: 800px;
                margin-left: auto;
                margin-right: auto;
            }

            .facilities-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 2rem;
            }

            .facility-item {
                background-color: #f5f5f5;
                border-radius: 0.5rem;
                padding: 1.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                text-align: center;
            }

            .facility-item:hover {
                transform: translateY(-5px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .facility-item h3 {
                font-family: 'Montserrat', sans-serif;
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0.75rem;
                color: #262626;
                text-align: center;
            }

            .facility-item p {
                color: #525252;
                font-size: 1rem;
                line-height: 1.5;
                margin-bottom: 0;
                text-align: center;
            }

            @media (max-width: 768px) {
                .facilities-grid {
                    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                    gap: 1.5rem;
                }

                .content-section h2 {
                    font-size: 1.75rem;
                }
            }

            @media (max-width: 480px) {
                .additional-content {
                    padding: 3rem 1rem;
                }

                .facilities-grid {
                    grid-template-columns: 1fr;
                }

                .content-section h2 {
                    font-size: 1.5rem;
                }
            }

            /* Language Switcher Styles */
            .language-switcher {
                margin-right: 1rem;
                z-index: 50;
                position: relative;
            }

            /* Ensure Flux dropdown menus are visible */
            .flux-dropdown-menu {
                z-index: 100;
            }

            /* Ensure Flux buttons in the language switcher look good */
            .language-switcher .flux-button {
                background-color: white;
                color: #262626;
                border: 1px solid #e5e5e5;
                border-radius: 0.375rem;
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .language-switcher .flux-button:hover {
                background-color: #f5f5f5;
            }
        </style>
    </head>
    <body>
        <!-- Header with navigation -->
        <header>
            <div class="navbar">
                <a href="{{ route('home') }}" class="navbar-brand">
                    <div class="logo-container">
                        <img src="{{ asset('images/sport-park-marie-pampoen.png') }}" alt="Sport Park Marie Pampoen Logo" class="logo-image rounded-md">
                        <span class="logo-text">Sport Park Marie Pampoen</span>
                    </div>
                </a>

                <div class="navbar-nav">
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <livewire:language-switcher />
                    </div>

                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}" class="btn-outline">
                                {{ __('Dashboard') }}
                            </a>
                        @else
                            <a href="{{ route('login') }}">
                                {{ __('Log In') }}
                            </a>

                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="btn-outline">
                                    {{ __('Register') }}
                                </a>
                            @endif
                        @endauth
                    @endif
                </div>
            </div>
        </header>

        <!-- Hero Section with Background Image -->
        <section class="hero-section">
            <!-- Background Image -->
            <div class="bg-image">
                <img src="{{ asset('images/soccer-field-ocean.jpg') }}" alt="Soccer Field Next to Ocean">
                <div class="overlay"></div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-wrapper">
                    <!-- Main Title -->
                    <h1>
                        {!! __('Reserve your field') !!}
                    </h1>

                    <!-- Subtitle -->
                    <p>
                        {{ __('Welcome to the online reservation system for Sport Park Marie Pampoen') }}
                    </p>

                    <!-- Call to Action Button -->
                    <div>
                        <a href="{{ Route::has('login') ? (Auth::check() ? route('dashboard') : route('login')) : '#' }}"
                           class="cta-button">
                            {{ Auth::check() ? __('Go to Dashboard') : __('Make a Reservation') }}
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Additional content to demonstrate parallax effect -->
        <div class="additional-content">
            <div class="content-section">
                <h2>{{ __('About Sport Park Marie Pampoen') }}</h2>
                <p>{{ __('Sport Park Marie Pampoen offers state-of-the-art facilities for sports enthusiasts of all levels. Our park features multiple soccer fields, multi-purpose courts, and recreational areas designed to provide the best sporting experience.') }}</p>
            </div>

            <div class="content-section">
                <h2>{{ __('Our Facilities') }}</h2>
                <div class="facilities-grid">
                    <div class="facility-item">
                        <h3>{{ __('Soccer Fields') }}</h3>
                        <p>{{ __('Professional-grade soccer fields with well-maintained grass and proper drainage systems.') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Multi-Purpose Courts') }}</h3>
                        <p>{{ __('Versatile courts that can be used for basketball, volleyball, and other team sports.') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Bolas Fields') }}</h3>
                        <p>{{ __('Dedicated areas for traditional bolas games with proper markings and equipment.') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Patios') }}</h3>
                        <p>{{ __('Comfortable patios for relaxation and social gatherings before or after sporting activities.') }}</p>
                    </div>
                </div>
            </div>

            <div class="content-section">
                <h2>{{ __('Weekly Events') }}</h2>
                <div class="facilities-grid">
                    <div class="facility-item">
                        <h3>{{ __('Domino Tuesdays') }}</h3>
                        <p>{{ __('Join us every Tuesday evening for exciting domino games and a fun social atmosphere. All skill levels welcome!') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Rugby Wednesdays') }}</h3>
                        <p>{{ __('Experience the thrill of rugby every Wednesday. Open to all ages and skill levels—come play or learn the game!') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Fitness Thursdays') }}</h3>
                        <p>{{ __('Stay fit with our group fitness sessions every Thursday. Open to all ages and fitness levels. Bring your energy!') }}</p>
                    </div>
                    <div class="facility-item">
                        <h3>{{ __('Soccer Tournaments on Saturdays') }}</h3>
                        <p>{{ __('Compete or cheer at our weekly soccer tournaments every Saturday. Teams and individuals can register to play!') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Parallax Effect Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const bgImage = document.querySelector('.bg-image img');
                const heroSection = document.querySelector('.hero-section');

                if (bgImage && heroSection) {
                    // Parallax factor (smaller = more subtle)
                    const parallaxFactor = 0.005; // Extremely subtle movement

                    // Get hero section dimensions
                    const heroWidth = heroSection.clientWidth;
                    const heroHeight = heroSection.clientHeight;

                    // Initial position (center)
                    let centerX = heroWidth / 2;
                    let centerY = heroHeight / 2;

                    // Update on mouse movement only when mouse is over hero section
                    heroSection.addEventListener('mousemove', function(e) {
                        // Get mouse position relative to hero section
                        const rect = heroSection.getBoundingClientRect();
                        const mouseX = e.clientX - rect.left;
                        const mouseY = e.clientY - rect.top;

                        // Calculate offset from center (as percentage of hero size)
                        const offsetX = (mouseX - centerX) / heroWidth;
                        const offsetY = (mouseY - centerY) / heroHeight;

                        // Apply parallax effect with subtle movement
                        // Use requestAnimationFrame for smoother performance
                        requestAnimationFrame(function() {
                            // Limit the movement to a few pixels for subtlety
                            const translateX = -offsetX * heroWidth * parallaxFactor;
                            const translateY = -offsetY * heroHeight * parallaxFactor;

                            bgImage.style.transform = `translate3d(${translateX}px, ${translateY}px, 0)`;
                        });
                    });

                    // Reset on window resize
                    window.addEventListener('resize', function() {
                        const newHeroWidth = heroSection.clientWidth;
                        const newHeroHeight = heroSection.clientHeight;
                        centerX = newHeroWidth / 2;
                        centerY = newHeroHeight / 2;
                    });
                }
            });
        </script>

        <!-- Livewire Scripts -->
        @livewireScripts

        <!-- Flux Scripts -->
        @fluxScripts
    </body>
</html>
