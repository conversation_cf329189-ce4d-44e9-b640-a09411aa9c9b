<?php

use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Language switcher route
Route::post('/set-language', function () {
    $locale = request()->input('locale');
    $validLocales = ['en', 'es', 'nl', 'pap', 'it']; // Added 'it' for Italian

    if (in_array($locale, $validLocales)) {
        session()->put('locale', $locale);
        app()->setLocale($locale);

        // Log the language change for debugging
        \Illuminate\Support\Facades\Log::info('Language changed to: ' . $locale);
    }

    // Get the previous URL or default to home
    $previousUrl = url()->previous() ?: route('home');

    // Redirect to the previous URL
    return redirect($previousUrl);
})->name('set.language');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password')->middleware('password.confirm');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Field routes
    Route::resource('fields', \App\Http\Controllers\FieldController::class);
    Route::get('fields/{field}/availability', [\App\Http\Controllers\FieldController::class, 'checkAvailability'])->name('fields.availability');
    Route::get('calendar/{field?}', [\App\Http\Controllers\FieldController::class, 'calendar'])->name('fields.calendar');
    Route::get('api/reservations', [\App\Http\Controllers\FieldController::class, 'getReservations'])->name('api.reservations');

    // Reservation routes
    Route::resource('reservations', \App\Http\Controllers\ReservationController::class);

    // Admin routes
    Route::middleware(['role:administrator,employee'])->prefix('admin')->name('admin.')->group(function () {
        Route::resource('fields', \App\Http\Controllers\Admin\FieldController::class);
        Route::resource('reservations', \App\Http\Controllers\Admin\ReservationController::class);
        Route::post('reservations/{reservation}/approve', [\App\Http\Controllers\Admin\ReservationController::class, 'approve'])->name('reservations.approve');
        Route::post('reservations/{reservation}/reject', [\App\Http\Controllers\Admin\ReservationController::class, 'reject'])->name('reservations.reject');

        // User management routes (admin only)
        Route::middleware(['role:administrator'])->group(function () {
            Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        });
    });
});

require __DIR__.'/auth.php';
